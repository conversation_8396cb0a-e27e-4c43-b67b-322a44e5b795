// Package dataconv 提供msgpack解码功能，支持结构体转换和未知字段处理
package dataconv

import (
	"bytes"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"
	"sync"

	"github.com/vmihailenco/msgpack/v5"
	"github.com/vmihailenco/msgpack/v5/msgpcode"
)

// 类型信息缓存
var typeCache sync.Map // map[reflect.Type]*structInfo

// 结构体字段信息
type fieldInfo struct {
	Index    int          // 字段在结构体中的索引
	Name     string       // 字段名
	IsStruct bool         // 是否是结构体
	IsPtr    bool         // 是否是指针
	ElemType reflect.Type // 如果是指针，指向的类型
}

// 字段转换信息
type fieldConvertInfo struct {
	FieldPath []int  // 字段路径
	ConvType  string // 转换类型：numberToString, stringToNumber
	Exclude   bool   // 是否排除在RawLog之外
}

// 结构体类型信息
type structInfo struct {
	Fields      map[string][]int                 // 字段名到字段路径的映射
	RawLogIndex []int                            // RawLog字段的路径，如果存在
	ConvertInfo map[string]*fieldConvertInfo    // 字段转换信息
}

// MsgpackDecode 解码msgpack数据到目标结构体
func MsgpackDecode(data []byte, v interface{}) error {
	decoder := newMsgpackDecoder(bytes.NewReader(data))
	return decoder.decode(v)
}

// 获取结构体类型信息，优先从缓存读取
func getStructInfo(t reflect.Type) *structInfo {
	if cached, ok := typeCache.Load(t); ok {
		return cached.(*structInfo)
	}

	info := &structInfo{
		Fields:      make(map[string][]int),
		ConvertInfo: make(map[string]*fieldConvertInfo),
	}

	var analyzeStruct func(reflect.Type, []int, string)
	analyzeStruct = func(t reflect.Type, index []int, prefix string) {
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			fieldIndex := append(append([]int{}, index...), i)

			if field.PkgPath != "" && !field.Anonymous {
				continue
			}

			tag := field.Tag.Get("msgpack")
			if tag == "-" && field.Name != "RawLog" {
				continue
			}

			fieldName := field.Name
			var convertType string
			var exclude bool

			if tag != "" {
				// 解析tag，支持格式：fieldname,convert=numberToString,exclude
				parts := strings.Split(tag, ",")
				if parts[0] != "" {
					fieldName = parts[0]
				}

				// 解析其他选项
				for i := 1; i < len(parts); i++ {
					part := strings.TrimSpace(parts[i])
					if strings.HasPrefix(part, "convert=") {
						convertType = strings.TrimPrefix(part, "convert=")
					} else if part == "exclude" {
						exclude = true
					}
				}
			}

			if field.Anonymous {
				fieldType := field.Type
				if fieldType.Kind() == reflect.Ptr {
					fieldType = fieldType.Elem()
				}

				if fieldType.Kind() == reflect.Struct {
					analyzeStruct(fieldType, fieldIndex, prefix)
					continue
				}
			}

			name := fieldName
			if prefix != "" {
				name = prefix + "." + name
			}

			if field.Name == "RawLog" && field.Type.Kind() == reflect.Map {
				info.RawLogIndex = fieldIndex
			}

			info.Fields[fieldName] = fieldIndex

			// 存储转换信息
			if convertType != "" || exclude {
				info.ConvertInfo[fieldName] = &fieldConvertInfo{
					FieldPath: fieldIndex,
					ConvType:  convertType,
					Exclude:   exclude,
				}
			}
		}
	}

	analyzeStruct(t, nil, "")
	typeCache.Store(t, info)

	return info
}

// convertValue 根据转换类型转换值
func convertValue(value interface{}, convType string) interface{} {
	switch convType {
	case "numberToString":
		switch v := value.(type) {
		case int:
			return strconv.Itoa(v)
		case int8:
			return strconv.FormatInt(int64(v), 10)
		case int16:
			return strconv.FormatInt(int64(v), 10)
		case int32:
			return strconv.FormatInt(int64(v), 10)
		case int64:
			return strconv.FormatInt(v, 10)
		case uint:
			return strconv.FormatUint(uint64(v), 10)
		case uint8:
			return strconv.FormatUint(uint64(v), 10)
		case uint16:
			return strconv.FormatUint(uint64(v), 10)
		case uint32:
			return strconv.FormatUint(uint64(v), 10)
		case uint64:
			return strconv.FormatUint(v, 10)
		case float32:
			return strconv.FormatFloat(float64(v), 'f', -1, 32)
		case float64:
			return strconv.FormatFloat(v, 'f', -1, 64)
		}
	case "stringToNumber":
		if str, ok := value.(string); ok {
			// 尝试解析为整数
			if i, err := strconv.ParseInt(str, 10, 64); err == nil {
				return i
			}
			// 尝试解析为浮点数
			if f, err := strconv.ParseFloat(str, 64); err == nil {
				return f
			}
		}
	}
	return value
}

// msgpackDecoder 解码器，封装msgpack解码逻辑
type msgpackDecoder struct {
	decoder *msgpack.Decoder // 底层msgpack解码器
}

// newMsgpackDecoder 创建新的解码器
func newMsgpackDecoder(r io.Reader) *msgpackDecoder {
	return &msgpackDecoder{
		decoder: msgpack.NewDecoder(r),
	}
}

// decode 解码msgpack数据到目标结构体
// 参数v必须是一个非空指针，指向要解码的目标结构体
// 支持嵌套结构体解码和未知字段存储到RawLog字段
func (d *msgpackDecoder) decode(v interface{}) error {
	rv := reflect.ValueOf(v)
	if rv.Kind() != reflect.Ptr || rv.IsNil() {
		return fmt.Errorf("target must be a non-nil pointer")
	}

	rv = rv.Elem()
	if rv.Kind() != reflect.Struct {
		return d.decoder.Decode(v)
	}

	mapLen, err := d.decoder.DecodeMapLen()
	if err != nil {
		return err
	}

	return d.decodeStructWithRawLog(rv, mapLen)
}

// decodeStructWithRawLog 解码带有RawLog字段的结构体
// 该方法处理msgpack map到结构体的转换，主要功能：
// 1. 将已知字段映射到结构体对应字段
// 2. 将未知字段存入RawLog
// 3. 处理嵌套结构体
// 示例：对于结构体{Name string; RawLog map[string]interface{}}，
// 如果msgpack包含{"Name":"John","Age":30}，则：
// - Name字段会被赋值"John"
// - Age字段会存入RawLog
func (d *msgpackDecoder) decodeStructWithRawLog(v reflect.Value, mapLen int) error {
	// 获取结构体元信息
	// info.Fields: 存储字段名到字段路径的映射，如{"Name": [0]}
	// info.RawLogIndex: 如果有RawLog字段，存储其位置，如[1]
	t := v.Type()
	info := getStructInfo(t)

	var rawLog reflect.Value
	if info.RawLogIndex != nil {
		// 获取RawLog字段的反射值
		rawLog = getFieldByPath(v, info.RawLogIndex)
		// 如果RawLog是nil则初始化
		if rawLog.IsNil() {
			rawLog.Set(reflect.MakeMap(rawLog.Type()))
		}
	}

	for i := 0; i < mapLen; i++ {
		// 遍历msgpack map中的每个键值对
		// 示例：对于{"Name":"John","Age":30}，会循环两次：
		// 第一次处理"Name":"John"
		// 第二次处理"Age":30
		key, err := d.decoder.DecodeString()
		if err != nil {
			return err
		}

		// 先解码值，用于后续处理
		var value interface{}
		if err := d.decoder.Decode(&value); err != nil {
			return err
		}

		// 处理已知字段
		if fieldPath, ok := info.Fields[key]; ok {
			// 获取字段反射值
			field := getFieldByPath(v, fieldPath)

			// 检查是否需要类型转换
			if convertInfo, hasConvert := info.ConvertInfo[key]; hasConvert && convertInfo.ConvType != "" {
				value = convertValue(value, convertInfo.ConvType)
			}

			// 处理嵌套结构体
			if field.Kind() == reflect.Struct ||
				(field.Kind() == reflect.Ptr && field.Type().Elem().Kind() == reflect.Struct) {

				// 检查值是否是map(可能为嵌套结构体)
				if valueMap, isMap := value.(map[string]interface{}); isMap {
					// 处理指针类型嵌套结构体
					if field.Kind() == reflect.Ptr {
						if field.IsNil() {
							field.Set(reflect.New(field.Type().Elem()))
						}
						field = field.Elem()
					}

					// 递归处理嵌套结构体
					nestedData, err := msgpack.Marshal(valueMap)
					if err != nil {
						return err
					}
					nestedDecoder := newMsgpackDecoder(bytes.NewReader(nestedData))
					if err := nestedDecoder.decode(field.Addr().Interface()); err != nil {
						return err
					}
				} else {
					// 直接设置值
					field.Set(reflect.ValueOf(value))
				}
			} else {
				// 设置普通字段值
				field.Set(reflect.ValueOf(value))
			}

			// 检查是否需要添加到RawLog（默认添加，除非明确exclude）
			if rawLog.IsValid() && !rawLog.IsNil() {
				if convertInfo, hasConvert := info.ConvertInfo[key]; !hasConvert || !convertInfo.Exclude {
					rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(value))
				}
			}
		} else if rawLog.IsValid() && !rawLog.IsNil() {
			// 处理未知字段(结构体中没有的字段)
			// 示例：结构体没有Age字段，则会将"Age":30存入RawLog
			rawLog.SetMapIndex(reflect.ValueOf(key), reflect.ValueOf(value))
		}
	}

	return nil
}

func getFieldByPath(v reflect.Value, path []int) reflect.Value {
	for _, i := range path {
		if v.Kind() == reflect.Ptr {
			if v.IsNil() {
				v.Set(reflect.New(v.Type().Elem()))
			}
			v = v.Elem()
		}
		v = v.Field(i)
	}
	return v
}

// isMapNext 检测下一个要解码的是否是map类型
// 返回值:
// bool: 是否是map
// int: map的长度
// error: 错误信息
// 支持检测FixedMap/Map16/Map32三种msgpack map格式
func (d *msgpackDecoder) isMapNext() (bool, int, error) {
	r, ok := d.decoder.Buffered().(*bytes.Reader)
	if !ok {
		return false, 0, fmt.Errorf("buffered reader is not a *bytes.Reader")
	}

	// pos, _ := r.Seek(0, io.SeekCurrent)
	code, err := d.decoder.PeekCode()
	if err != nil {
		return false, 0, err
	}

	// _, err = r.Seek(pos, io.SeekStart)
	// if err != nil {
	// 	return false, 0, err
	// }

	var mapLen int
	isMap := false

	if code >= msgpcode.FixedMapLow && code <= msgpcode.FixedMapHigh {
		mapLen = int(code & msgpcode.FixedMapMask)
		isMap = true
	} else if code == msgpcode.Map16 || code == msgpcode.Map32 {
		isMap = true

		pos, _ := r.Seek(0, io.SeekCurrent)
		mapLen, err = d.decoder.DecodeMapLen()
		if err != nil {
			return false, 0, err
		}

		_, err = r.Seek(pos, io.SeekStart)
		if err != nil {
			return false, 0, err
		}
	}

	return isMap, mapLen, nil
}
